import pandas as pd
from pptx import Presentation

def verify_no_truncation():
    """Verify that NO content is truncated in the PowerPoint"""
    print("=== Verifying NO Truncation in PowerPoint ===")
    
    # Read Excel data as reference
    df = pd.read_excel("Project Downtime Logs - Copy.xlsx")
    change_requests = df[df['Incident'] == 'Changes Request']
    
    print(f"Excel change requests: {len(change_requests)} items")
    
    # Check PowerPoint file
    try:
        prs = Presentation("Project_Updates_Full_Content.pptx")
        print(f"PowerPoint slides: {len(prs.slides)}")
        
        # Find change requests slide
        change_requests_slide = None
        for slide in prs.slides:
            for shape in slide.shapes:
                if hasattr(shape, 'text') and 'Change Requests Details' in shape.text:
                    change_requests_slide = slide
                    break
            if change_requests_slide:
                break
        
        if change_requests_slide:
            print("\n✅ Found Change Requests slide")
            
            # Find the table
            for shape in change_requests_slide.shapes:
                if hasattr(shape, 'table'):
                    table = shape.table
                    print(f"Table: {len(table.rows)} rows x {len(table.columns)} columns")
                    print(f"Data rows: {len(table.rows) - 1}")
                    
                    print("\n=== Content Verification (NO Ellipsis Check) ===")
                    
                    # Check each row for complete content
                    for i, (_, excel_row) in enumerate(change_requests.iterrows()):
                        if i + 1 < len(table.rows):  # +1 for header
                            ppt_desc = table.cell(i + 1, 1).text  # Description column
                            excel_desc = str(excel_row['Description'])
                            
                            # Check for ellipsis or truncation
                            has_ellipsis = '...' in ppt_desc
                            is_complete = excel_desc.strip() == ppt_desc.strip()
                            
                            print(f"\nRow {i+1} (#{excel_row['#']}):")
                            print(f"  Excel length: {len(excel_desc)} characters")
                            print(f"  PPT length:   {len(ppt_desc)} characters")
                            print(f"  Has ellipsis: {'❌ YES' if has_ellipsis else '✅ NO'}")
                            print(f"  Complete:     {'✅ YES' if is_complete else '⚠️ DIFFERENT'}")
                            
                            if not is_complete and not has_ellipsis:
                                print(f"  Excel: {excel_desc}")
                                print(f"  PPT:   {ppt_desc}")
                    
                    break
        
        # Check all slides for any ellipsis
        print(f"\n=== Global Ellipsis Check ===")
        total_ellipsis = 0
        
        for i, slide in enumerate(prs.slides):
            slide_ellipsis = 0
            for shape in slide.shapes:
                if hasattr(shape, 'text') and '...' in shape.text:
                    slide_ellipsis += shape.text.count('...')
                elif hasattr(shape, 'table'):
                    table = shape.table
                    for row_idx in range(len(table.rows)):
                        for col_idx in range(len(table.columns)):
                            cell_text = table.cell(row_idx, col_idx).text
                            if '...' in cell_text:
                                slide_ellipsis += cell_text.count('...')
            
            if slide_ellipsis > 0:
                print(f"  Slide {i+1}: Found {slide_ellipsis} ellipsis")
                total_ellipsis += slide_ellipsis
        
        if total_ellipsis == 0:
            print("  ✅ NO ellipsis found in entire presentation!")
        else:
            print(f"  ❌ Found {total_ellipsis} ellipsis in presentation")
    
    except Exception as e:
        print(f"Error: {e}")

def show_complete_content_sample():
    """Show complete content from Excel for comparison"""
    print(f"\n=== Complete Excel Content Sample ===")
    df = pd.read_excel("Project Downtime Logs - Copy.xlsx")
    change_requests = df[df['Incident'] == 'Changes Request']
    
    print("Complete Change Requests content (first 2 items):")
    for i, (_, row) in enumerate(change_requests.head(2).iterrows()):
        print(f"\n{i+1}. #{row['#']} - {row['Incident']}")
        print(f"   Description (COMPLETE): {row['Description']}")
        print(f"   Status: {row['Status']}")
        print(f"   Open Date: {row['Open Date']}")
        print(f"   Action Taken: {row['Action Taken']}")
        print(f"   Closed On: {row['Closed On']}")

if __name__ == "__main__":
    verify_no_truncation()
    show_complete_content_sample()
