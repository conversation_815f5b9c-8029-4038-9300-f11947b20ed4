import pandas as pd
from pptx import Presentation

def verify_complete_data():
    """验证完整版PowerPoint是否包含所有数据"""
    print("=== 验证完整版PowerPoint数据 ===")
    
    # 读取Excel数据作为参考
    df = pd.read_excel("Project Downtime Logs - Copy.xlsx")
    change_requests = df[df['Incident'] == 'Changes Request']
    
    print(f"Excel中的变更请求总数: {len(change_requests)} 条")
    
    # 检查PowerPoint文件
    try:
        prs = Presentation("Project_Updates_Complete.pptx")
        print(f"PowerPoint幻灯片数量: {len(prs.slides)}")
        
        # 查找变更请求页面（通常是第5页，索引为4）
        for i, slide in enumerate(prs.slides):
            print(f"\n--- 幻灯片 {i+1} ---")
            
            for j, shape in enumerate(slide.shapes):
                if hasattr(shape, 'text') and '变更请求详情' in shape.text:
                    print(f"找到变更请求页面！")
                    
                    # 查找该页面的表格
                    for k, table_shape in enumerate(slide.shapes):
                        if hasattr(table_shape, 'table'):
                            table = table_shape.table
                            print(f"变更请求表格: {len(table.rows)} 行 x {len(table.columns)} 列")
                            print(f"数据行数: {len(table.rows) - 1} 行（减去表头）")
                            
                            # 显示所有行的内容
                            print("表格内容:")
                            for row_idx in range(len(table.rows)):
                                row_content = []
                                for col_idx in range(len(table.columns)):
                                    cell_text = table.cell(row_idx, col_idx).text.strip()
                                    row_content.append(cell_text[:30] + "..." if len(cell_text) > 30 else cell_text)
                                
                                if row_idx == 0:
                                    print(f"  表头: {' | '.join(row_content)}")
                                else:
                                    print(f"  数据行 {row_idx}: {' | '.join(row_content)}")
                            
                            # 验证数据完整性
                            data_rows = len(table.rows) - 1
                            if data_rows == len(change_requests):
                                print(f"✅ 验证成功！PowerPoint包含所有 {len(change_requests)} 条变更请求")
                            else:
                                print(f"❌ 数据不完整！PowerPoint只有 {data_rows} 条，应该有 {len(change_requests)} 条")
                            
                            break
                    break
        
        # 检查其他页面
        print(f"\n=== 所有页面概览 ===")
        for i, slide in enumerate(prs.slides):
            title_text = ""
            table_count = 0
            
            for shape in slide.shapes:
                if hasattr(shape, 'text') and shape.text.strip():
                    if '详情' in shape.text or '统计' in shape.text or '目录' in shape.text or '报告' in shape.text:
                        title_text = shape.text.strip()
                        break
                elif hasattr(shape, 'table'):
                    table_count += 1
            
            print(f"幻灯片 {i+1}: {title_text[:50]}... (表格数: {table_count})")
    
    except Exception as e:
        print(f"验证PowerPoint文件时出错: {e}")

if __name__ == "__main__":
    verify_complete_data()
