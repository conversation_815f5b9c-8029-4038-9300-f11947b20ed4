import pandas as pd
import openpyxl
from openpyxl import load_workbook
import json
from datetime import datetime, date

def analyze_excel_file(file_path):
    """分析Excel文件的结构和内容"""
    print(f"正在分析文件: {file_path}")
    
    # 使用openpyxl加载工作簿
    wb = load_workbook(file_path, data_only=True)
    
    print(f"工作表数量: {len(wb.sheetnames)}")
    print(f"工作表名称: {wb.sheetnames}")
    
    all_data = {}
    
    for sheet_name in wb.sheetnames:
        print(f"\n--- 分析工作表: {sheet_name} ---")
        ws = wb[sheet_name]
        
        # 获取工作表的尺寸
        max_row = ws.max_row
        max_col = ws.max_column
        print(f"最大行数: {max_row}, 最大列数: {max_col}")
        
        # 读取前几行数据来了解结构
        sheet_data = []
        for row in range(1, min(max_row + 1, 21)):  # 读取前20行
            row_data = []
            for col in range(1, max_col + 1):
                cell_value = ws.cell(row=row, column=col).value
                row_data.append(cell_value)
            sheet_data.append(row_data)
        
        all_data[sheet_name] = sheet_data
        
        # 显示前几行数据
        print("前几行数据:")
        for i, row in enumerate(sheet_data[:10]):
            print(f"第{i+1}行: {row}")
    
    return all_data

def analyze_with_pandas(file_path):
    """使用pandas分析Excel文件"""
    print(f"\n=== 使用pandas分析 {file_path} ===")
    
    # 读取所有工作表
    excel_file = pd.ExcelFile(file_path)
    print(f"工作表名称: {excel_file.sheet_names}")
    
    all_dataframes = {}
    
    for sheet_name in excel_file.sheet_names:
        print(f"\n--- 工作表: {sheet_name} ---")
        df = pd.read_excel(file_path, sheet_name=sheet_name)
        
        print(f"数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        print("前5行数据:")
        print(df.head())
        
        print("\n数据类型:")
        print(df.dtypes)
        
        print("\n数据概览:")
        print(df.describe(include='all'))
        
        all_dataframes[sheet_name] = df
    
    return all_dataframes

if __name__ == "__main__":
    file_path = "Project Downtime Logs - Copy.xlsx"
    
    # 使用openpyxl分析
    excel_data = analyze_excel_file(file_path)
    
    # 使用pandas分析
    dataframes = analyze_with_pandas(file_path)
    
    # 保存分析结果到JSON文件
    with open("excel_analysis.json", "w", encoding="utf-8") as f:
        # 将pandas DataFrame转换为可序列化的格式
        serializable_data = {}
        for sheet_name, df in dataframes.items():
            # 转换日期时间类型为字符串
            df_copy = df.copy()
            for col in df_copy.columns:
                if df_copy[col].dtype == 'datetime64[ns]':
                    df_copy[col] = df_copy[col].dt.strftime('%Y-%m-%d %H:%M:%S')
                elif df_copy[col].dtype == 'object':
                    # 检查是否包含datetime对象
                    df_copy[col] = df_copy[col].apply(lambda x: x.strftime('%Y-%m-%d %H:%M:%S') if isinstance(x, (datetime, date)) else x)

            serializable_data[sheet_name] = {
                "columns": list(df_copy.columns),
                "shape": df_copy.shape,
                "data": df_copy.fillna("").to_dict('records')[:20]  # 只保存前20行
            }
        json.dump(serializable_data, f, ensure_ascii=False, indent=2)
    
    print("\n分析完成！结果已保存到 excel_analysis.json")
