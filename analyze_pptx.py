from pptx import Presentation
import json

def analyze_pptx_template(file_path):
    """分析PowerPoint模板的结构"""
    print(f"正在分析PowerPoint文件: {file_path}")
    
    try:
        prs = Presentation(file_path)
        
        print(f"幻灯片数量: {len(prs.slides)}")
        
        template_info = {
            "slide_count": len(prs.slides),
            "slides": []
        }
        
        for i, slide in enumerate(prs.slides):
            print(f"\n--- 幻灯片 {i+1} ---")
            
            slide_info = {
                "slide_number": i + 1,
                "layout_name": slide.slide_layout.name if hasattr(slide.slide_layout, 'name') else "Unknown",
                "shapes": []
            }
            
            print(f"布局名称: {slide_info['layout_name']}")
            print(f"形状数量: {len(slide.shapes)}")
            
            for j, shape in enumerate(slide.shapes):
                shape_info = {
                    "shape_id": j + 1,
                    "shape_type": str(shape.shape_type),
                    "has_text": hasattr(shape, 'text'),
                    "text_content": ""
                }
                
                if hasattr(shape, 'text'):
                    shape_info["text_content"] = shape.text
                    print(f"  形状 {j+1}: {shape.shape_type} - 文本: '{shape.text[:50]}...' " if len(shape.text) > 50 else f"  形状 {j+1}: {shape.shape_type} - 文本: '{shape.text}'")
                else:
                    print(f"  形状 {j+1}: {shape.shape_type} - 无文本")
                
                # 检查是否有表格
                if hasattr(shape, 'table'):
                    print(f"    包含表格: {shape.table.rows} 行 x {shape.table.columns} 列")
                    shape_info["is_table"] = True
                    shape_info["table_rows"] = len(shape.table.rows)
                    shape_info["table_columns"] = len(shape.table.columns)
                else:
                    shape_info["is_table"] = False
                
                slide_info["shapes"].append(shape_info)
            
            template_info["slides"].append(slide_info)
        
        return template_info
        
    except Exception as e:
        print(f"分析PowerPoint文件时出错: {e}")
        return None

if __name__ == "__main__":
    file_path = "Project_Updates_Template_With_TOC.pptx"
    
    template_info = analyze_pptx_template(file_path)
    
    if template_info:
        # 保存分析结果
        with open("pptx_analysis.json", "w", encoding="utf-8") as f:
            json.dump(template_info, f, ensure_ascii=False, indent=2)
        
        print("\n分析完成！结果已保存到 pptx_analysis.json")
    else:
        print("分析失败！")
