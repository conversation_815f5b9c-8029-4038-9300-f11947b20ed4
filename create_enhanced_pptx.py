import pandas as pd
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor
from datetime import datetime
import json

def load_excel_data(file_path):
    """加载Excel数据"""
    df = pd.read_excel(file_path)
    return df

def categorize_incidents(df):
    """将事件按类型分类"""
    system_outages = df[df['Incident'] == 'System outages'].copy()
    change_requests = df[df['Incident'] == 'Changes Request'].copy()
    issues = df[df['Incident'] == 'Issue'].copy()
    
    return system_outages, change_requests, issues

def format_cell_text(cell, text, font_size=12, bold=False):
    """格式化单元格文本"""
    cell.text = text
    for paragraph in cell.text_frame.paragraphs:
        for run in paragraph.runs:
            run.font.size = Pt(font_size)
            run.font.bold = bold

def create_comprehensive_presentation(excel_file, output_file):
    """创建完整的演示文稿"""
    print("正在加载数据...")
    
    # 加载Excel数据
    df = load_excel_data(excel_file)
    
    # 按类型分类
    system_outages, change_requests, issues = categorize_incidents(df)
    
    print(f"系统故障: {len(system_outages)} 条")
    print(f"变更请求: {len(change_requests)} 条")
    print(f"问题: {len(issues)} 条")
    
    # 创建新的演示文稿
    prs = Presentation()
    
    # 1. 创建标题页
    title_slide_layout = prs.slide_layouts[0]  # Title Slide
    slide = prs.slides.add_slide(title_slide_layout)
    title = slide.shapes.title
    subtitle = slide.placeholders[1]
    
    title.text = "项目更新报告"
    subtitle.text = f"""项目停机日志分析
    
总计事件: {len(df)} 条
- 系统故障: {len(system_outages)} 条
- 变更请求: {len(change_requests)} 条  
- 问题: {len(issues)} 条

生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M')}"""
    
    # 2. 创建目录页
    bullet_slide_layout = prs.slide_layouts[1]  # Title and Content
    slide = prs.slides.add_slide(bullet_slide_layout)
    title = slide.shapes.title
    content = slide.placeholders[1]
    
    title.text = "目录"
    tf = content.text_frame
    tf.text = "1. 概览统计"
    
    p = tf.add_paragraph()
    p.text = "2. 系统故障详情"
    p.level = 0
    
    p = tf.add_paragraph()
    p.text = "3. 变更请求详情"
    p.level = 0
    
    p = tf.add_paragraph()
    p.text = "4. 问题详情"
    p.level = 0
    
    # 3. 创建概览统计页
    slide = prs.slides.add_slide(bullet_slide_layout)
    title = slide.shapes.title
    title.text = "概览统计"
    
    # 添加统计表格
    rows, cols = 5, 3
    left = Inches(1)
    top = Inches(2)
    width = Inches(8)
    height = Inches(4)
    
    table = slide.shapes.add_table(rows, cols, left, top, width, height).table
    
    # 设置表头
    headers = ["类型", "数量", "百分比"]
    for i, header in enumerate(headers):
        format_cell_text(table.cell(0, i), header, 14, True)
    
    # 填充统计数据
    total = len(df)
    stats_data = [
        ("系统故障", len(system_outages), f"{len(system_outages)/total*100:.1f}%"),
        ("变更请求", len(change_requests), f"{len(change_requests)/total*100:.1f}%"),
        ("问题", len(issues), f"{len(issues)/total*100:.1f}%"),
        ("总计", total, "100.0%")
    ]
    
    for i, (type_name, count, percentage) in enumerate(stats_data):
        format_cell_text(table.cell(i+1, 0), type_name)
        format_cell_text(table.cell(i+1, 1), str(count))
        format_cell_text(table.cell(i+1, 2), percentage)
    
    return prs, system_outages, change_requests, issues

def add_detailed_slides(prs, system_outages, change_requests, issues):
    """添加详细信息页面"""

    # 4. 系统故障详情页
    if len(system_outages) > 0:
        slide = prs.slides.add_slide(prs.slide_layouts[5])  # Blank layout

        # 添加标题
        title_shape = slide.shapes.add_textbox(Inches(0.5), Inches(0.5), Inches(9), Inches(1))
        title_frame = title_shape.text_frame
        title_frame.text = "系统故障详情"
        for paragraph in title_frame.paragraphs:
            for run in paragraph.runs:
                run.font.size = Pt(24)
                run.font.bold = True

        # 添加详情表格
        rows = min(len(system_outages) + 1, 8)  # 最多显示7条记录
        cols = 5
        table = slide.shapes.add_table(rows, cols, Inches(0.5), Inches(1.5), Inches(9), Inches(5)).table

        # 表头
        headers = ["#", "描述", "根本原因", "开始时间", "状态"]
        for i, header in enumerate(headers):
            format_cell_text(table.cell(0, i), header, 12, True)

        # 数据
        for i, (_, row) in enumerate(system_outages.head(7).iterrows()):
            format_cell_text(table.cell(i+1, 0), str(row.get('#', '')))
            desc = str(row.get('Description', ''))[:50] + "..." if len(str(row.get('Description', ''))) > 50 else str(row.get('Description', ''))
            format_cell_text(table.cell(i+1, 1), desc)
            format_cell_text(table.cell(i+1, 2), str(row.get('Root Cause', '')))
            format_cell_text(table.cell(i+1, 3), str(row.get('Open Date', '')))
            format_cell_text(table.cell(i+1, 4), str(row.get('Status', '')))

    # 5. 变更请求详情页
    if len(change_requests) > 0:
        slide = prs.slides.add_slide(prs.slide_layouts[5])  # Blank layout

        # 添加标题
        title_shape = slide.shapes.add_textbox(Inches(0.5), Inches(0.5), Inches(9), Inches(1))
        title_frame = title_shape.text_frame
        title_frame.text = "变更请求详情"
        for paragraph in title_frame.paragraphs:
            for run in paragraph.runs:
                run.font.size = Pt(24)
                run.font.bold = True

        # 添加详情表格
        rows = min(len(change_requests) + 1, 8)  # 最多显示7条记录
        cols = 4
        table = slide.shapes.add_table(rows, cols, Inches(0.5), Inches(1.5), Inches(9), Inches(5)).table

        # 表头
        headers = ["#", "描述", "开始时间", "状态"]
        for i, header in enumerate(headers):
            format_cell_text(table.cell(0, i), header, 12, True)

        # 数据
        for i, (_, row) in enumerate(change_requests.head(7).iterrows()):
            format_cell_text(table.cell(i+1, 0), str(row.get('#', '')))
            desc = str(row.get('Description', ''))[:60] + "..." if len(str(row.get('Description', ''))) > 60 else str(row.get('Description', ''))
            format_cell_text(table.cell(i+1, 1), desc)
            format_cell_text(table.cell(i+1, 2), str(row.get('Open Date', '')))
            format_cell_text(table.cell(i+1, 3), str(row.get('Status', '')))

    # 6. 问题详情页
    if len(issues) > 0:
        slide = prs.slides.add_slide(prs.slide_layouts[5])  # Blank layout

        # 添加标题
        title_shape = slide.shapes.add_textbox(Inches(0.5), Inches(0.5), Inches(9), Inches(1))
        title_frame = title_shape.text_frame
        title_frame.text = "问题详情"
        for paragraph in title_frame.paragraphs:
            for run in paragraph.runs:
                run.font.size = Pt(24)
                run.font.bold = True

        # 添加详情表格
        rows = min(len(issues) + 1, 8)  # 最多显示7条记录
        cols = 5
        table = slide.shapes.add_table(rows, cols, Inches(0.5), Inches(1.5), Inches(9), Inches(5)).table

        # 表头
        headers = ["#", "描述", "采取行动", "关闭时间", "状态"]
        for i, header in enumerate(headers):
            format_cell_text(table.cell(0, i), header, 12, True)

        # 数据
        for i, (_, row) in enumerate(issues.head(7).iterrows()):
            format_cell_text(table.cell(i+1, 0), str(row.get('#', '')))
            desc = str(row.get('Description', ''))[:40] + "..." if len(str(row.get('Description', ''))) > 40 else str(row.get('Description', ''))
            format_cell_text(table.cell(i+1, 1), desc)
            action = str(row.get('Action Taken', ''))[:40] + "..." if len(str(row.get('Action Taken', ''))) > 40 else str(row.get('Action Taken', ''))
            format_cell_text(table.cell(i+1, 2), action)
            format_cell_text(table.cell(i+1, 3), str(row.get('Closed On', '')))
            format_cell_text(table.cell(i+1, 4), str(row.get('Status', '')))

if __name__ == "__main__":
    excel_file = "Project Downtime Logs - Copy.xlsx"
    output_file = "Project_Updates_Enhanced.pptx"
    
    # 创建基础演示文稿
    prs, system_outages, change_requests, issues = create_comprehensive_presentation(excel_file, output_file)
    
    # 添加详细页面
    add_detailed_slides(prs, system_outages, change_requests, issues)
    
    # 保存演示文稿
    prs.save(output_file)
    print(f"增强版演示文稿已保存为: {output_file}")
    
    # 输出数据摘要
    print("\n=== 数据摘要 ===")
    print("系统故障:")
    for _, row in system_outages.iterrows():
        print(f"  - {row['Description'][:50]}... (状态: {row['Status']})")
    
    print("\n变更请求:")
    for _, row in change_requests.head(5).iterrows():
        print(f"  - {row['Description'][:50]}... (状态: {row['Status']})")
    
    print("\n问题:")
    for _, row in issues.iterrows():
        print(f"  - {row['Description'][:50]}... (状态: {row['Status']})")
