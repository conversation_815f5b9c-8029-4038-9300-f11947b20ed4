import pandas as pd
from pptx import Presentation
from pptx.util import Inches
from datetime import datetime
import json

def load_excel_data(file_path):
    """加载Excel数据"""
    df = pd.read_excel(file_path)
    return df

def categorize_incidents(df):
    """将事件按类型分类"""
    system_outages = df[df['Incident'] == 'System outages'].copy()
    change_requests = df[df['Incident'] == 'Changes Request'].copy()
    issues = df[df['Incident'] == 'Issue'].copy()
    
    return system_outages, change_requests, issues

def populate_table(table, data, columns):
    """填充表格数据"""
    # 设置表头
    for j, col in enumerate(columns):
        if j < len(table.columns):
            table.cell(0, j).text = col

    # 限制数据行数以适应现有表格
    max_data_rows = len(table.rows) - 1  # 减去表头行
    limited_data = data[:max_data_rows] if len(data) > max_data_rows else data

    # 填充数据行
    for i, row_data in enumerate(limited_data):
        row_index = i + 1  # 跳过表头
        for j, col in enumerate(columns):
            if j < len(table.columns):
                cell_value = str(row_data.get(col, '')) if pd.notna(row_data.get(col, '')) else ''
                # 限制文本长度以适应表格
                if len(cell_value) > 100:
                    cell_value = cell_value[:97] + "..."
                table.cell(row_index, j).text = cell_value

    # 清空剩余的行
    for i in range(len(limited_data) + 1, len(table.rows)):
        for j in range(len(table.columns)):
            table.cell(i, j).text = ""

def create_updated_presentation(excel_file, template_file, output_file):
    """创建更新后的演示文稿"""
    print("正在加载数据...")
    
    # 加载Excel数据
    df = load_excel_data(excel_file)
    
    # 按类型分类
    system_outages, change_requests, issues = categorize_incidents(df)
    
    print(f"系统故障: {len(system_outages)} 条")
    print(f"变更请求: {len(change_requests)} 条")
    print(f"问题: {len(issues)} 条")
    
    # 加载PowerPoint模板
    prs = Presentation(template_file)
    
    # 更新目录页面（第1页）
    title_slide = prs.slides[0]
    for shape in title_slide.shapes:
        if hasattr(shape, 'text') and 'Table of Contents' in shape.text:
            shape.text = f"""Table of Contents
1. System Outages ({len(system_outages)} items)
2. Change Requests ({len(change_requests)} items)
3. Issues ({len(issues)} items)

Last Updated: {datetime.now().strftime('%Y-%m-%d %H:%M')}"""
    
    # 填充系统故障页面（第2页）
    if len(prs.slides) > 1:
        outages_slide = prs.slides[1]
        for shape in outages_slide.shapes:
            if hasattr(shape, 'table'):
                outages_data = system_outages.to_dict('records')
                columns = ['#', 'Description', 'Root Cause', 'Open Date', 'Status']
                populate_table(shape.table, outages_data, columns)
    
    # 填充变更请求页面（第3页）
    if len(prs.slides) > 2:
        changes_slide = prs.slides[2]
        for shape in changes_slide.shapes:
            if hasattr(shape, 'table'):
                changes_data = change_requests.to_dict('records')
                columns = ['#', 'Description', 'Status']
                populate_table(shape.table, changes_data, columns)
    
    # 填充问题页面（第4页）
    if len(prs.slides) > 3:
        issues_slide = prs.slides[3]
        for shape in issues_slide.shapes:
            if hasattr(shape, 'table'):
                issues_data = issues.to_dict('records')
                columns = ['#', 'Description', 'Action Taken', 'Status']
                populate_table(shape.table, issues_data, columns)
    
    # 保存更新后的演示文稿
    prs.save(output_file)
    print(f"演示文稿已保存为: {output_file}")

if __name__ == "__main__":
    excel_file = "Project Downtime Logs - Copy.xlsx"
    template_file = "Project_Updates_Template_With_TOC.pptx"
    output_file = "Project_Updates_Populated.pptx"
    
    create_updated_presentation(excel_file, template_file, output_file)
