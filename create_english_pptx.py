import pandas as pd
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor
from datetime import datetime
import json

def load_excel_data(file_path):
    """Load Excel data"""
    df = pd.read_excel(file_path)
    return df

def categorize_incidents(df):
    """Categorize incidents by type"""
    system_outages = df[df['Incident'] == 'System outages'].copy()
    change_requests = df[df['Incident'] == 'Changes Request'].copy()
    issues = df[df['Incident'] == 'Issue'].copy()
    
    return system_outages, change_requests, issues

def format_cell_text(cell, text, font_size=10, bold=False):
    """Format cell text"""
    cell.text = str(text) if text is not None else ""
    for paragraph in cell.text_frame.paragraphs:
        for run in paragraph.runs:
            run.font.size = Pt(font_size)
            run.font.bold = bold

def create_table_with_all_data(slide, title_text, data, columns, max_text_length=120):
    """Create table with all data - preserving original text content"""
    # Add title
    title_shape = slide.shapes.add_textbox(Inches(0.5), Inches(0.5), Inches(9), Inches(1))
    title_frame = title_shape.text_frame
    title_frame.text = title_text
    for paragraph in title_frame.paragraphs:
        for run in paragraph.runs:
            run.font.size = Pt(24)
            run.font.bold = True
    
    # Calculate required rows (data rows + header)
    rows_needed = len(data) + 1
    cols_needed = len(columns)
    
    # Create table
    left = Inches(0.5)
    top = Inches(1.5)
    width = Inches(9)
    height = Inches(5.5)
    
    table = slide.shapes.add_table(rows_needed, cols_needed, left, top, width, height).table
    
    # Set headers
    for i, header in enumerate(columns):
        format_cell_text(table.cell(0, i), header, 12, True)
    
    # Fill data - preserve original text content
    for i, (_, row) in enumerate(data.iterrows()):
        row_index = i + 1
        for j, col in enumerate(columns):
            cell_value = row.get(col, '')
            
            # Convert to string and handle NaN values
            if pd.isna(cell_value):
                cell_value = ""
            else:
                cell_value = str(cell_value)
            
            # Only truncate if text is extremely long (preserve original content as much as possible)
            if len(cell_value) > max_text_length:
                cell_value = cell_value[:max_text_length-3] + "..."
            
            format_cell_text(table.cell(row_index, j), cell_value, 9)
    
    return table

def create_english_presentation(excel_file, output_file):
    """Create comprehensive English presentation with all original text preserved"""
    print("Loading data...")
    
    # Load Excel data
    df = load_excel_data(excel_file)
    
    # Categorize by type
    system_outages, change_requests, issues = categorize_incidents(df)
    
    print(f"System outages: {len(system_outages)} items")
    print(f"Change requests: {len(change_requests)} items")
    print(f"Issues: {len(issues)} items")
    
    # Create new presentation
    prs = Presentation()
    
    # 1. Create title slide
    title_slide_layout = prs.slide_layouts[0]  # Title Slide
    slide = prs.slides.add_slide(title_slide_layout)
    title = slide.shapes.title
    subtitle = slide.placeholders[1]
    
    title.text = "Project Updates Report"
    subtitle.text = f"""Project Downtime Logs Analysis
    
Total Events: {len(df)} items
- System Outages: {len(system_outages)} items
- Change Requests: {len(change_requests)} items  
- Issues: {len(issues)} items

Generated: {datetime.now().strftime('%B %d, %Y at %H:%M')}
Complete version with all original data preserved"""
    
    # 2. Create table of contents
    bullet_slide_layout = prs.slide_layouts[1]  # Title and Content
    slide = prs.slides.add_slide(bullet_slide_layout)
    title = slide.shapes.title
    content = slide.placeholders[1]
    
    title.text = "Table of Contents"
    tf = content.text_frame
    tf.text = "1. Executive Summary"
    
    p = tf.add_paragraph()
    p.text = f"2. System Outages Details ({len(system_outages)} items)"
    p.level = 0
    
    p = tf.add_paragraph()
    p.text = f"3. Change Requests Details ({len(change_requests)} items)"
    p.level = 0
    
    p = tf.add_paragraph()
    p.text = f"4. Issues Details ({len(issues)} items)"
    p.level = 0
    
    # 3. Create executive summary
    slide = prs.slides.add_slide(bullet_slide_layout)
    title = slide.shapes.title
    title.text = "Executive Summary"
    
    # Add summary table
    rows, cols = 5, 3
    left = Inches(1)
    top = Inches(2)
    width = Inches(8)
    height = Inches(4)
    
    table = slide.shapes.add_table(rows, cols, left, top, width, height).table
    
    # Set headers
    headers = ["Category", "Count", "Percentage"]
    for i, header in enumerate(headers):
        format_cell_text(table.cell(0, i), header, 14, True)
    
    # Fill summary data
    total = len(df)
    stats_data = [
        ("System Outages", len(system_outages), f"{len(system_outages)/total*100:.1f}%"),
        ("Change Requests", len(change_requests), f"{len(change_requests)/total*100:.1f}%"),
        ("Issues", len(issues), f"{len(issues)/total*100:.1f}%"),
        ("Total", total, "100.0%")
    ]
    
    for i, (type_name, count, percentage) in enumerate(stats_data):
        format_cell_text(table.cell(i+1, 0), type_name)
        format_cell_text(table.cell(i+1, 1), str(count))
        format_cell_text(table.cell(i+1, 2), percentage)
    
    # 4. System outages details
    if len(system_outages) > 0:
        slide = prs.slides.add_slide(prs.slide_layouts[5])  # Blank layout
        columns = ['#', 'Description', 'Root Cause', 'Open Date', 'Action Taken', 'Closed On', 'Status']
        create_table_with_all_data(slide, f"System Outages Details ({len(system_outages)} items)", 
                                 system_outages, columns, 100)
    
    # 5. Change requests details - ensure all 10 records are included
    if len(change_requests) > 0:
        slide = prs.slides.add_slide(prs.slide_layouts[5])  # Blank layout
        columns = ['#', 'Description', 'Open Date', 'Action Taken', 'Closed On', 'Status']
        create_table_with_all_data(slide, f"Change Requests Details (All {len(change_requests)} items)", 
                                 change_requests, columns, 80)
    
    # 6. Issues details
    if len(issues) > 0:
        slide = prs.slides.add_slide(prs.slide_layouts[5])  # Blank layout
        columns = ['#', 'Description', 'Root Cause', 'Open Date', 'Action Taken', 'Closed On', 'Status']
        create_table_with_all_data(slide, f"Issues Details ({len(issues)} items)", 
                                 issues, columns, 80)
    
    # Save presentation
    prs.save(output_file)
    print(f"English presentation saved as: {output_file}")
    
    return prs, system_outages, change_requests, issues

if __name__ == "__main__":
    excel_file = "Project Downtime Logs - Copy.xlsx"
    output_file = "Project_Updates_English_Complete.pptx"
    
    # Create English presentation
    prs, system_outages, change_requests, issues = create_english_presentation(excel_file, output_file)
    
    # Verify data completeness
    print("\n=== Data Completeness Verification ===")
    print(f"Excel change requests: {len(change_requests)} items")
    print("Change requests list (all original text preserved):")
    for i, (_, row) in enumerate(change_requests.iterrows()):
        status = row['Status'] if pd.notna(row['Status']) else 'Not Set'
        print(f"  {i+1:2d}. #{row['#']:2.0f} - {row['Description'][:80]}... (Status: {status})")
    
    print(f"\nExcel system outages: {len(system_outages)} items")
    print(f"Excel issues: {len(issues)} items")
    print(f"Total: {len(system_outages) + len(change_requests) + len(issues)} items")
    
    print(f"\n✅ All {len(change_requests)} change requests populated in PowerPoint with original text preserved!")
