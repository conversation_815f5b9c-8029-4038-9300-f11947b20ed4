import pandas as pd
from datetime import datetime
import json

def generate_summary_report():
    """生成项目总结报告"""
    
    print("=" * 60)
    print("项目停机日志分析 - 总结报告")
    print("=" * 60)
    print(f"生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}")
    print()
    
    # 读取Excel数据
    df = pd.read_excel("Project Downtime Logs - Copy.xlsx")
    
    print("📊 数据概览:")
    print(f"   总记录数: {len(df)} 条")
    print()
    
    # 按事件类型分类统计
    incident_counts = df['Incident'].value_counts()
    print("📈 事件类型分布:")
    for incident_type, count in incident_counts.items():
        percentage = (count / len(df)) * 100
        print(f"   {incident_type}: {count} 条 ({percentage:.1f}%)")
    print()
    
    # 按状态分类统计
    status_counts = df['Status'].value_counts()
    print("📋 状态分布:")
    for status, count in status_counts.items():
        if pd.notna(status):
            percentage = (count / len(df)) * 100
            print(f"   {status}: {count} 条 ({percentage:.1f}%)")
    print()
    
    # 最近的事件
    print("🕒 最近的事件 (前5条):")
    # 尝试解析日期
    df_copy = df.copy()
    df_copy['Open Date'] = pd.to_datetime(df_copy['Open Date'], errors='coerce')
    recent_events = df_copy.dropna(subset=['Open Date']).sort_values('Open Date', ascending=False).head(5)
    
    for _, row in recent_events.iterrows():
        date_str = row['Open Date'].strftime('%Y-%m-%d') if pd.notna(row['Open Date']) else 'N/A'
        print(f"   {date_str}: {row['Incident']} - {row['Description'][:50]}...")
    print()
    
    # 生成的文件
    print("📁 生成的文件:")
    print("   1. Project_Updates_Populated.pptx - 基于模板填充的PowerPoint")
    print("   2. Project_Updates_Enhanced.pptx - 增强版PowerPoint (推荐)")
    print("   3. excel_analysis.json - Excel数据分析结果")
    print("   4. pptx_analysis.json - PowerPoint模板分析结果")
    print()
    
    print("✅ 完成的任务:")
    print("   ✓ 读取并分析Excel文件数据")
    print("   ✓ 分析PowerPoint模板结构")
    print("   ✓ 将Excel数据按类型分类")
    print("   ✓ 生成基础版PowerPoint演示文稿")
    print("   ✓ 创建增强版PowerPoint演示文稿，包含:")
    print("     - 标题页面")
    print("     - 目录页面")
    print("     - 统计概览页面")
    print("     - 系统故障详情页面")
    print("     - 变更请求详情页面")
    print("     - 问题详情页面")
    print()
    
    print("🎯 推荐使用:")
    print("   Project_Updates_Enhanced.pptx - 这是最完整的版本，包含所有数据和格式化")
    print()
    
    print("📝 数据质量说明:")
    print("   - 部分记录的日期格式不一致")
    print("   - 部分记录缺少状态信息")
    print("   - 建议在源数据中统一日期格式和状态字段")
    print()
    
    # 保存详细统计到JSON
    summary_stats = {
        "total_records": len(df),
        "incident_types": incident_counts.to_dict(),
        "status_distribution": status_counts.to_dict(),
        "generation_time": datetime.now().isoformat(),
        "files_generated": [
            "Project_Updates_Populated.pptx",
            "Project_Updates_Enhanced.pptx",
            "excel_analysis.json",
            "pptx_analysis.json"
        ]
    }
    
    with open("summary_stats.json", "w", encoding="utf-8") as f:
        json.dump(summary_stats, f, ensure_ascii=False, indent=2)
    
    print("📊 详细统计已保存到: summary_stats.json")
    print("=" * 60)

if __name__ == "__main__":
    generate_summary_report()
