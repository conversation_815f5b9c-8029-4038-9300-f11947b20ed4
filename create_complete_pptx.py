import pandas as pd
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor
from datetime import datetime
import json

def load_excel_data(file_path):
    """加载Excel数据"""
    df = pd.read_excel(file_path)
    return df

def categorize_incidents(df):
    """将事件按类型分类"""
    system_outages = df[df['Incident'] == 'System outages'].copy()
    change_requests = df[df['Incident'] == 'Changes Request'].copy()
    issues = df[df['Incident'] == 'Issue'].copy()
    
    return system_outages, change_requests, issues

def format_cell_text(cell, text, font_size=10, bold=False):
    """格式化单元格文本"""
    cell.text = str(text) if text is not None else ""
    for paragraph in cell.text_frame.paragraphs:
        for run in paragraph.runs:
            run.font.size = Pt(font_size)
            run.font.bold = bold

def create_table_with_all_data(slide, title_text, data, columns, table_position):
    """创建包含所有数据的表格"""
    # 添加标题
    title_shape = slide.shapes.add_textbox(Inches(0.5), Inches(0.5), Inches(9), Inches(1))
    title_frame = title_shape.text_frame
    title_frame.text = title_text
    for paragraph in title_frame.paragraphs:
        for run in paragraph.runs:
            run.font.size = Pt(24)
            run.font.bold = True
    
    # 计算需要的行数（数据行 + 表头）
    rows_needed = len(data) + 1
    cols_needed = len(columns)
    
    # 创建表格
    left = Inches(0.5)
    top = Inches(1.5)
    width = Inches(9)
    height = Inches(5.5)
    
    table = slide.shapes.add_table(rows_needed, cols_needed, left, top, width, height).table
    
    # 设置表头
    for i, header in enumerate(columns):
        format_cell_text(table.cell(0, i), header, 12, True)
    
    # 填充数据
    for i, (_, row) in enumerate(data.iterrows()):
        row_index = i + 1
        for j, col in enumerate(columns):
            cell_value = row.get(col, '')
            # 处理长文本
            if isinstance(cell_value, str) and len(cell_value) > 80:
                cell_value = cell_value[:77] + "..."
            format_cell_text(table.cell(row_index, j), cell_value, 9)
    
    return table

def create_comprehensive_presentation_v2(excel_file, output_file):
    """创建包含所有数据的完整演示文稿"""
    print("正在加载数据...")
    
    # 加载Excel数据
    df = load_excel_data(excel_file)
    
    # 按类型分类
    system_outages, change_requests, issues = categorize_incidents(df)
    
    print(f"系统故障: {len(system_outages)} 条")
    print(f"变更请求: {len(change_requests)} 条")
    print(f"问题: {len(issues)} 条")
    
    # 创建新的演示文稿
    prs = Presentation()
    
    # 1. 创建标题页
    title_slide_layout = prs.slide_layouts[0]  # Title Slide
    slide = prs.slides.add_slide(title_slide_layout)
    title = slide.shapes.title
    subtitle = slide.placeholders[1]
    
    title.text = "项目更新报告 - 完整版"
    subtitle.text = f"""项目停机日志分析
    
总计事件: {len(df)} 条
- 系统故障: {len(system_outages)} 条
- 变更请求: {len(change_requests)} 条  
- 问题: {len(issues)} 条

生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M')}
包含所有数据的完整版本"""
    
    # 2. 创建目录页
    bullet_slide_layout = prs.slide_layouts[1]  # Title and Content
    slide = prs.slides.add_slide(bullet_slide_layout)
    title = slide.shapes.title
    content = slide.placeholders[1]
    
    title.text = "目录"
    tf = content.text_frame
    tf.text = "1. 概览统计"
    
    p = tf.add_paragraph()
    p.text = f"2. 系统故障详情 ({len(system_outages)} 条)"
    p.level = 0
    
    p = tf.add_paragraph()
    p.text = f"3. 变更请求详情 ({len(change_requests)} 条)"
    p.level = 0
    
    p = tf.add_paragraph()
    p.text = f"4. 问题详情 ({len(issues)} 条)"
    p.level = 0
    
    # 3. 创建概览统计页
    slide = prs.slides.add_slide(bullet_slide_layout)
    title = slide.shapes.title
    title.text = "概览统计"
    
    # 添加统计表格
    rows, cols = 5, 3
    left = Inches(1)
    top = Inches(2)
    width = Inches(8)
    height = Inches(4)
    
    table = slide.shapes.add_table(rows, cols, left, top, width, height).table
    
    # 设置表头
    headers = ["类型", "数量", "百分比"]
    for i, header in enumerate(headers):
        format_cell_text(table.cell(0, i), header, 14, True)
    
    # 填充统计数据
    total = len(df)
    stats_data = [
        ("系统故障", len(system_outages), f"{len(system_outages)/total*100:.1f}%"),
        ("变更请求", len(change_requests), f"{len(change_requests)/total*100:.1f}%"),
        ("问题", len(issues), f"{len(issues)/total*100:.1f}%"),
        ("总计", total, "100.0%")
    ]
    
    for i, (type_name, count, percentage) in enumerate(stats_data):
        format_cell_text(table.cell(i+1, 0), type_name)
        format_cell_text(table.cell(i+1, 1), str(count))
        format_cell_text(table.cell(i+1, 2), percentage)
    
    # 4. 系统故障详情页
    if len(system_outages) > 0:
        slide = prs.slides.add_slide(prs.slide_layouts[5])  # Blank layout
        columns = ['#', 'Description', 'Root Cause', 'Open Date', 'Status']
        create_table_with_all_data(slide, "系统故障详情", system_outages, columns, 1)
    
    # 5. 变更请求详情页 - 确保包含所有10条记录
    if len(change_requests) > 0:
        slide = prs.slides.add_slide(prs.slide_layouts[5])  # Blank layout
        columns = ['#', 'Description', 'Open Date', 'Status']
        create_table_with_all_data(slide, f"变更请求详情 (共{len(change_requests)}条)", change_requests, columns, 2)
    
    # 6. 问题详情页
    if len(issues) > 0:
        slide = prs.slides.add_slide(prs.slide_layouts[5])  # Blank layout
        columns = ['#', 'Description', 'Action Taken', 'Closed On', 'Status']
        create_table_with_all_data(slide, "问题详情", issues, columns, 3)
    
    # 保存演示文稿
    prs.save(output_file)
    print(f"完整版演示文稿已保存为: {output_file}")
    
    return prs, system_outages, change_requests, issues

if __name__ == "__main__":
    excel_file = "Project Downtime Logs - Copy.xlsx"
    output_file = "Project_Updates_Complete.pptx"
    
    # 创建完整版演示文稿
    prs, system_outages, change_requests, issues = create_comprehensive_presentation_v2(excel_file, output_file)
    
    # 验证数据完整性
    print("\n=== 数据完整性验证 ===")
    print(f"Excel中的变更请求: {len(change_requests)} 条")
    print("变更请求列表:")
    for i, (_, row) in enumerate(change_requests.iterrows()):
        print(f"  {i+1}. #{row['#']} - {row['Description'][:60]}... (状态: {row['Status']})")
    
    print(f"\nExcel中的系统故障: {len(system_outages)} 条")
    print(f"Excel中的问题: {len(issues)} 条")
    print(f"总计: {len(system_outages) + len(change_requests) + len(issues)} 条")
    
    print(f"\n✅ 所有 {len(change_requests)} 条变更请求已填充到PowerPoint中！")
