import pandas as pd
from pptx import Presentation

def check_excel_data():
    """检查Excel数据的完整性"""
    print("=== 检查Excel数据 ===")
    df = pd.read_excel("Project Downtime Logs - Copy.xlsx")
    
    # 按类型分类
    system_outages = df[df['Incident'] == 'System outages']
    change_requests = df[df['Incident'] == 'Changes Request']
    issues = df[df['Incident'] == 'Issue']
    
    print(f"总记录数: {len(df)}")
    print(f"系统故障: {len(system_outages)} 条")
    print(f"变更请求: {len(change_requests)} 条")
    print(f"问题: {len(issues)} 条")
    
    print("\n=== 变更请求详细列表 ===")
    for i, (_, row) in enumerate(change_requests.iterrows()):
        print(f"{i+1}. #{row['#']} - {row['Description'][:80]}... (状态: {row['Status']})")
    
    print("\n=== 系统故障详细列表 ===")
    for i, (_, row) in enumerate(system_outages.iterrows()):
        print(f"{i+1}. #{row['#']} - {row['Description'][:80]}... (状态: {row['Status']})")
    
    print("\n=== 问题详细列表 ===")
    for i, (_, row) in enumerate(issues.iterrows()):
        print(f"{i+1}. #{row['#']} - {row['Description'][:80]}... (状态: {row['Status']})")
    
    return df, system_outages, change_requests, issues

def check_pptx_content(file_path):
    """检查PowerPoint文件内容"""
    print(f"\n=== 检查PowerPoint文件: {file_path} ===")
    
    try:
        prs = Presentation(file_path)
        print(f"幻灯片数量: {len(prs.slides)}")
        
        for i, slide in enumerate(prs.slides):
            print(f"\n--- 幻灯片 {i+1} ---")
            for j, shape in enumerate(slide.shapes):
                if hasattr(shape, 'text') and shape.text.strip():
                    print(f"  文本形状 {j+1}: {shape.text[:100]}...")
                elif hasattr(shape, 'table'):
                    table = shape.table
                    print(f"  表格 {j+1}: {len(table.rows)} 行 x {len(table.columns)} 列")
                    
                    # 检查表格内容
                    for row_idx in range(min(len(table.rows), 5)):  # 只显示前5行
                        row_content = []
                        for col_idx in range(len(table.columns)):
                            cell_text = table.cell(row_idx, col_idx).text.strip()
                            row_content.append(cell_text[:20] + "..." if len(cell_text) > 20 else cell_text)
                        print(f"    行 {row_idx+1}: {' | '.join(row_content)}")
                    
                    if len(table.rows) > 5:
                        print(f"    ... 还有 {len(table.rows) - 5} 行")
    
    except Exception as e:
        print(f"检查PowerPoint文件时出错: {e}")

if __name__ == "__main__":
    # 检查Excel数据
    df, system_outages, change_requests, issues = check_excel_data()
    
    # 检查生成的PowerPoint文件
    check_pptx_content("Project_Updates_Enhanced.pptx")
    
    print(f"\n=== 数据对比 ===")
    print(f"Excel中的变更请求: {len(change_requests)} 条")
    print("请检查PowerPoint中变更请求页面的表格是否包含所有数据")
