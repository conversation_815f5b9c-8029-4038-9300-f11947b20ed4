import pandas as pd
from pptx import Presentation

def verify_english_presentation():
    """Verify English presentation contains all original text content"""
    print("=== Verifying English PowerPoint Data ===")
    
    # Read Excel data as reference
    df = pd.read_excel("Project Downtime Logs - Copy.xlsx")
    change_requests = df[df['Incident'] == 'Changes Request']
    system_outages = df[df['Incident'] == 'System outages']
    issues = df[df['Incident'] == 'Issue']
    
    print(f"Excel change requests total: {len(change_requests)} items")
    print(f"Excel system outages total: {len(system_outages)} items")
    print(f"Excel issues total: {len(issues)} items")
    
    # Check PowerPoint file
    try:
        prs = Presentation("Project_Updates_English_Complete.pptx")
        print(f"PowerPoint slides count: {len(prs.slides)}")
        
        # Find and verify each section
        for i, slide in enumerate(prs.slides):
            print(f"\n--- Slide {i+1} ---")
            
            slide_title = ""
            for shape in slide.shapes:
                if hasattr(shape, 'text') and shape.text.strip():
                    if any(keyword in shape.text.lower() for keyword in ['change requests', 'system outages', 'issues details']):
                        slide_title = shape.text.strip()
                        print(f"Found section: {slide_title}")
                        
                        # Find table in this slide
                        for table_shape in slide.shapes:
                            if hasattr(table_shape, 'table'):
                                table = table_shape.table
                                data_rows = len(table.rows) - 1  # Exclude header
                                print(f"Table: {len(table.rows)} rows x {len(table.columns)} columns")
                                print(f"Data rows: {data_rows}")
                                
                                # Show sample of original text preservation
                                if 'Change Requests' in slide_title:
                                    print(f"✅ Change Requests: Found {data_rows} items (Expected: {len(change_requests)})")
                                    if data_rows == len(change_requests):
                                        print("✅ All change requests included!")
                                    
                                    # Show first few rows to verify original text
                                    print("Sample original text content:")
                                    for row_idx in range(1, min(4, len(table.rows))):
                                        desc_cell = table.cell(row_idx, 1).text  # Description column
                                        print(f"  Row {row_idx}: {desc_cell[:100]}...")
                                
                                elif 'System Outages' in slide_title:
                                    print(f"✅ System Outages: Found {data_rows} items (Expected: {len(system_outages)})")
                                    
                                elif 'Issues' in slide_title:
                                    print(f"✅ Issues: Found {data_rows} items (Expected: {len(issues)})")
                                
                                break
                        break
        
        # Detailed verification of change requests original text
        print(f"\n=== Detailed Change Requests Verification ===")
        print("Comparing Excel vs PowerPoint content:")
        
        # Find change requests slide
        change_requests_slide = None
        for slide in prs.slides:
            for shape in slide.shapes:
                if hasattr(shape, 'text') and 'Change Requests Details' in shape.text:
                    change_requests_slide = slide
                    break
            if change_requests_slide:
                break
        
        if change_requests_slide:
            # Find the table
            for shape in change_requests_slide.shapes:
                if hasattr(shape, 'table'):
                    table = shape.table
                    print(f"Found change requests table with {len(table.rows)} rows")
                    
                    # Compare with Excel data
                    for i, (_, excel_row) in enumerate(change_requests.iterrows()):
                        if i + 1 < len(table.rows):  # +1 for header
                            ppt_desc = table.cell(i + 1, 1).text  # Description column
                            excel_desc = str(excel_row['Description'])
                            
                            # Check if original text is preserved (allowing for truncation)
                            if excel_desc in ppt_desc or ppt_desc in excel_desc:
                                print(f"  ✅ Row {i+1}: Original text preserved")
                            else:
                                print(f"  ⚠️  Row {i+1}: Text may be modified")
                                print(f"     Excel: {excel_desc[:50]}...")
                                print(f"     PPT:   {ppt_desc[:50]}...")
                    break
    
    except Exception as e:
        print(f"Error verifying PowerPoint file: {e}")

def show_original_content_sample():
    """Show sample of original content from Excel"""
    print(f"\n=== Original Excel Content Sample ===")
    df = pd.read_excel("Project Downtime Logs - Copy.xlsx")
    change_requests = df[df['Incident'] == 'Changes Request']
    
    print("Original Change Requests text (first 3 items):")
    for i, (_, row) in enumerate(change_requests.head(3).iterrows()):
        print(f"\n{i+1}. #{row['#']} - {row['Incident']}")
        print(f"   Description: {row['Description']}")
        print(f"   Status: {row['Status']}")
        print(f"   Open Date: {row['Open Date']}")

if __name__ == "__main__":
    verify_english_presentation()
    show_original_content_sample()
