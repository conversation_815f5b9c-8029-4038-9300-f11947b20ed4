{"Sheet1": {"columns": ["#", "Incident", "Description", "Root Cause", "Open Date", "Action Taken", "Closed On", "Status"], "shape": [14, 8], "data": [{"#": 1, "Incident": "System outages", "Description": "BPTS - DOWN\ncannot get into DocXtract.", "Root Cause": "The HTTPS certificate was expired", "Open Date": "12/15/2023, 12:32 PM", "Action Taken": "Identified an issue with the certificate being applied correctly and have now added an audit step as verification moving forward.", "Closed On": "12/15/2023, 12:51 PM", "Status": "Resolved"}, {"#": 2, "Incident": "Changes Request", "Description": "Clean up the historical email logs and keep the emails for one year only", "Root Cause": "", "Open Date": "", "Action Taken": "", "Closed On": "2024-04-20 00:00:00", "Status": "In Prod"}, {"#": 3, "Incident": "Changes Request", "Description": "BOOMI Integration \n the file extension will only change to.xml when BOOMI returns the response file to your response location", "Root Cause": "", "Open Date": "2024-01-24 00:00:00", "Action Taken": "Nothing was changed on DocXtract end", "Closed On": "", "Status": "Closed"}, {"#": 4, "Incident": "Changes Request", "Description": "SSO setup\nsetup of Single Sign On through Azure AD", "Root Cause": "", "Open Date": "2024-02-06 00:00:00", "Action Taken": "", "Closed On": "2024-04-20 00:00:00", "Status": "In Prod"}, {"#": 5, "Incident": "Changes Request", "Description": "Security Question\nTo create a new role in Exactech to just get access to the full Req list", "Root Cause": "", "Open Date": "2024-02-08 00:00:00", "Action Taken": "", "Closed On": "", "Status": "Not Started"}, {"#": 6, "Incident": "Changes Request", "Description": "Timing of file export to DocXtract\nmove the nightly file drops that we send you from 7pm to 8pm.", "Root Cause": "", "Open Date": "2024-07-12 00:00:00", "Action Taken": "Updated the time for the monitoring service accordingly", "Closed On": "2024-07-15 00:00:00", "Status": ""}, {"#": 7, "Incident": "Issue", "Description": "email approvals not showing up\nEmail Service not available", "Root Cause": "", "Open Date": "2024-12-04 00:00:00", "Action Taken": "The email security key was expired", "Closed On": "2024-12-04 00:00:00", "Status": "Resolved"}, {"#": 8, "Incident": "Changes Request", "Description": "Exactech: Requisition submit approval issue ", "Root Cause": "", "Open Date": "2025-02-13 00:00:00", "Action Taken": "Confirmed it was not an issue. User is not  allowed to approve the requistion that he created", "Closed On": "2025-02-13 00:00:00", "Status": "Closed"}, {"#": 9, "Incident": "Changes Request", "Description": "BPTS Server Maintenance - Mar 11, 2am-5am ET ", "Root Cause": "", "Open Date": "2025-03-11 00:00:00", "Action Taken": "", "Closed On": "2025-03-11 00:00:00", "Status": "Closed"}, {"#": 10, "Incident": "Issue", "Description": "Email approvals not coming in", "Root Cause": "", "Open Date": "2025-04-28 00:00:00", "Action Taken": "There was a server maintenance update made on 3/10 and it caused the connection between the email service and DocXtract to not function properly.", "Closed On": "2025-04-29 00:00:00", "Status": "Resolved"}, {"#": 11, "Incident": "Changes Request", "Description": "Email Monitoring\nAn alert to our internal team is put in place when no emails are sent and/or received in DocXtract for 2 business days. When the alert is received, we will check to see if there are any issues and resolve.", "Root Cause": "", "Open Date": "2025-04-28 00:00:00", "Action Taken": "", "Closed On": "2025-04-29 00:00:00", "Status": "In Prod"}, {"#": 12, "Incident": "System outages", "Description": "Exactech: DocXtract Production issue - URGENT\ncannot get into DocXtract.", "Root Cause": "It was an issue with the IIS", "Open Date": "5/1/2025, 8:28 AM", "Action Taken": "", "Closed On": "5/1/2025, 8:54 AM", "Status": "Resolved"}, {"#": 13, "Incident": "Changes Request", "Description": "Forgot Password and Account Lockout", "Root Cause": "", "Open Date": "2025-05-06 00:00:00", "Action Taken": "Updated the login screen to leave SSO option only", "Closed On": "2025-05-27 00:00:00", "Status": "In Prod"}, {"#": 14, "Incident": "Changes Request", "Description": "Exactech -> NewCo", "Root Cause": "", "Open Date": "2025-07-02 00:00:00", "Action Taken": "", "Closed On": "", "Status": ""}]}}