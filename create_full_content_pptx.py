import pandas as pd
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor
from datetime import datetime
import json

def load_excel_data(file_path):
    """Load Excel data"""
    df = pd.read_excel(file_path)
    return df

def categorize_incidents(df):
    """Categorize incidents by type"""
    system_outages = df[df['Incident'] == 'System outages'].copy()
    change_requests = df[df['Incident'] == 'Changes Request'].copy()
    issues = df[df['Incident'] == 'Issue'].copy()
    
    return system_outages, change_requests, issues

def format_cell_text(cell, text, font_size=9, bold=False):
    """Format cell text - preserve all content without truncation"""
    # Convert to string and handle NaN values
    if pd.isna(text):
        cell_text = ""
    else:
        cell_text = str(text)
    
    cell.text = cell_text
    
    # Set text wrapping and formatting
    cell.text_frame.word_wrap = True
    cell.text_frame.auto_size = None
    
    for paragraph in cell.text_frame.paragraphs:
        for run in paragraph.runs:
            run.font.size = Pt(font_size)
            run.font.bold = bold

def create_table_with_full_content(slide, title_text, data, columns):
    """Create table with complete content - NO truncation or ellipsis"""
    # Add title
    title_shape = slide.shapes.add_textbox(Inches(0.5), Inches(0.3), Inches(9), Inches(0.8))
    title_frame = title_shape.text_frame
    title_frame.text = title_text
    for paragraph in title_frame.paragraphs:
        for run in paragraph.runs:
            run.font.size = Pt(20)
            run.font.bold = True
    
    # Calculate required rows (data rows + header)
    rows_needed = len(data) + 1
    cols_needed = len(columns)
    
    # Create table with more space for content
    left = Inches(0.3)
    top = Inches(1.2)
    width = Inches(9.4)
    height = Inches(6)
    
    table = slide.shapes.add_table(rows_needed, cols_needed, left, top, width, height).table
    
    # Set column widths to accommodate full content
    if len(columns) == 6:  # Change requests
        table.columns[0].width = Inches(0.5)  # #
        table.columns[1].width = Inches(4.0)  # Description
        table.columns[2].width = Inches(1.5)  # Open Date
        table.columns[3].width = Inches(2.0)  # Action Taken
        table.columns[4].width = Inches(1.0)  # Closed On
        table.columns[5].width = Inches(0.9)  # Status
    elif len(columns) == 7:  # System outages or Issues
        table.columns[0].width = Inches(0.4)  # #
        table.columns[1].width = Inches(3.0)  # Description
        table.columns[2].width = Inches(1.5)  # Root Cause
        table.columns[3].width = Inches(1.2)  # Open Date
        table.columns[4].width = Inches(2.0)  # Action Taken
        table.columns[5].width = Inches(1.0)  # Closed On
        table.columns[6].width = Inches(0.8)  # Status
    
    # Set headers
    for i, header in enumerate(columns):
        format_cell_text(table.cell(0, i), header, 10, True)
        # Set header row height
        table.rows[0].height = Inches(0.4)
    
    # Fill data - preserve ALL original text content
    for i, (_, row) in enumerate(data.iterrows()):
        row_index = i + 1
        
        # Set row height to accommodate content
        table.rows[row_index].height = Inches(0.8)
        
        for j, col in enumerate(columns):
            cell_value = row.get(col, '')
            
            # NO truncation - preserve complete original content
            format_cell_text(table.cell(row_index, j), cell_value, 8)
    
    return table

def create_full_content_presentation(excel_file, output_file):
    """Create presentation with complete original content - NO truncation"""
    print("Loading data...")
    
    # Load Excel data
    df = load_excel_data(excel_file)
    
    # Categorize by type
    system_outages, change_requests, issues = categorize_incidents(df)
    
    print(f"System outages: {len(system_outages)} items")
    print(f"Change requests: {len(change_requests)} items")
    print(f"Issues: {len(issues)} items")
    
    # Create new presentation
    prs = Presentation()
    
    # 1. Create title slide
    title_slide_layout = prs.slide_layouts[0]  # Title Slide
    slide = prs.slides.add_slide(title_slide_layout)
    title = slide.shapes.title
    subtitle = slide.placeholders[1]
    
    title.text = "Project Updates Report"
    subtitle.text = f"""Project Downtime Logs Analysis

Total Events: {len(df)} items
- System Outages: {len(system_outages)} items
- Change Requests: {len(change_requests)} items  
- Issues: {len(issues)} items

Generated: {datetime.now().strftime('%B %d, %Y at %H:%M')}
Complete version with ALL original content preserved"""
    
    # 2. Create table of contents
    bullet_slide_layout = prs.slide_layouts[1]  # Title and Content
    slide = prs.slides.add_slide(bullet_slide_layout)
    title = slide.shapes.title
    content = slide.placeholders[1]
    
    title.text = "Table of Contents"
    tf = content.text_frame
    tf.text = "1. Executive Summary"
    
    p = tf.add_paragraph()
    p.text = f"2. System Outages Details ({len(system_outages)} items)"
    p.level = 0
    
    p = tf.add_paragraph()
    p.text = f"3. Change Requests Details ({len(change_requests)} items)"
    p.level = 0
    
    p = tf.add_paragraph()
    p.text = f"4. Issues Details ({len(issues)} items)"
    p.level = 0
    
    # 3. Create executive summary
    slide = prs.slides.add_slide(bullet_slide_layout)
    title = slide.shapes.title
    title.text = "Executive Summary"
    
    # Add summary table
    rows, cols = 5, 3
    left = Inches(1)
    top = Inches(2)
    width = Inches(8)
    height = Inches(4)
    
    table = slide.shapes.add_table(rows, cols, left, top, width, height).table
    
    # Set headers
    headers = ["Category", "Count", "Percentage"]
    for i, header in enumerate(headers):
        format_cell_text(table.cell(0, i), header, 14, True)
    
    # Fill summary data
    total = len(df)
    stats_data = [
        ("System Outages", len(system_outages), f"{len(system_outages)/total*100:.1f}%"),
        ("Change Requests", len(change_requests), f"{len(change_requests)/total*100:.1f}%"),
        ("Issues", len(issues), f"{len(issues)/total*100:.1f}%"),
        ("Total", total, "100.0%")
    ]
    
    for i, (type_name, count, percentage) in enumerate(stats_data):
        format_cell_text(table.cell(i+1, 0), type_name)
        format_cell_text(table.cell(i+1, 1), str(count))
        format_cell_text(table.cell(i+1, 2), percentage)
    
    # 4. System outages details - FULL content
    if len(system_outages) > 0:
        slide = prs.slides.add_slide(prs.slide_layouts[5])  # Blank layout
        columns = ['#', 'Description', 'Root Cause', 'Open Date', 'Action Taken', 'Closed On', 'Status']
        create_table_with_full_content(slide, f"System Outages Details ({len(system_outages)} items)", 
                                     system_outages, columns)
    
    # 5. Change requests details - FULL content, all 10 records
    if len(change_requests) > 0:
        slide = prs.slides.add_slide(prs.slide_layouts[5])  # Blank layout
        columns = ['#', 'Description', 'Open Date', 'Action Taken', 'Closed On', 'Status']
        create_table_with_full_content(slide, f"Change Requests Details (All {len(change_requests)} items)", 
                                     change_requests, columns)
    
    # 6. Issues details - FULL content
    if len(issues) > 0:
        slide = prs.slides.add_slide(prs.slide_layouts[5])  # Blank layout
        columns = ['#', 'Description', 'Root Cause', 'Open Date', 'Action Taken', 'Closed On', 'Status']
        create_table_with_full_content(slide, f"Issues Details ({len(issues)} items)", 
                                     issues, columns)
    
    # Save presentation
    prs.save(output_file)
    print(f"Full content presentation saved as: {output_file}")
    
    return prs, system_outages, change_requests, issues

if __name__ == "__main__":
    excel_file = "Project Downtime Logs - Copy.xlsx"
    output_file = "Project_Updates_Full_Content.pptx"
    
    # Create presentation with full content
    prs, system_outages, change_requests, issues = create_full_content_presentation(excel_file, output_file)
    
    # Verify no content truncation
    print("\n=== Full Content Verification ===")
    print(f"✅ All {len(change_requests)} change requests with COMPLETE original text")
    print(f"✅ All {len(system_outages)} system outages with COMPLETE original text")
    print(f"✅ All {len(issues)} issues with COMPLETE original text")
    print(f"✅ NO truncation or ellipsis used - ALL content preserved")
    
    print("\nSample of preserved content:")
    for i, (_, row) in enumerate(change_requests.head(3).iterrows()):
        print(f"\n{i+1}. #{row['#']} - Full Description:")
        print(f"   {row['Description']}")
        print(f"   Status: {row['Status']}")
    
    print(f"\n🎉 Complete! Use {output_file} - contains ALL original text without any truncation!")
